# Proof Tree: AM-GM Sum-to-Product Inequality

## ROOT_001 [ROOT]
**Theorem**: Given non-negative reals a₁,...,aₙ with a₁+⋯+aₙ = n, prove that a₁a₂⋯aₙ ≤ 1.

**Parent Node**: None

**Status**: [ROOT]

---

## STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001

**Detailed Plan**: Use AM-GM inequality approach as primary strategy. Apply the arithmetic-mean ≥ geometric-mean inequality to establish the bound.

**Strategy**: Apply AM-GM: (a₁+⋯+aₙ)/n ≥ (a₁a₂⋯aₙ)^{1/n}, then use constraint a₁+⋯+aₙ = n.

**Status**: [TO_EXPLORE]

---

## SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001

**Goal**: Establish the AM-GM inequality for n non-negative reals.

**Strategy**: Use Mathlib's `Real.geom_mean_le_arith_mean_weighted` directly with uniform weights 1/n. This gives ∏ i ∈ s, z i ^ w i ≤ ∑ i ∈ s, w i * z i.

**Failure Reason**: Complex type issues with Finset membership and weight sum calculations. Need simpler approach.

**Status**: [DEAD_END]

---

## SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001

**Goal**: Apply the constraint a₁+⋯+aₙ = n to simplify the left side of AM-GM.

**Strategy**: Substitute the sum constraint into the AM-GM inequality.

**Status**: [TO_EXPLORE]

---

## SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001

**Goal**: Derive a₁a₂⋯aₙ ≤ 1 from (a₁a₂⋯aₙ)^{1/n} ≤ 1.

**Strategy**: Use power properties and the fact that x^{1/n} ≤ 1 implies x ≤ 1 for non-negative x.

**Status**: [TO_EXPLORE]

---

## STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001

**Detailed Plan**: Alternative approach using Jensen's inequality on the logarithm function.

**Strategy**: Use concavity of ln x on (0,∞) and Jensen's inequality to establish ln(a₁a₂⋯aₙ) ≤ 0.

**Status**: [TO_EXPLORE]

---

## SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_002

**Goal**: Apply Jensen's inequality to ln function.

**Strategy**: Use (ln a₁+⋯+ln aₙ)/n ≤ ln[(a₁+⋯+aₙ)/n] due to concavity of ln.

**Status**: [TO_EXPLORE]

---

## SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_002

**Goal**: Handle the case where some aᵢ = 0.

**Strategy**: Show that if any aᵢ = 0, then the product is 0 ≤ 1.

**Status**: [TO_EXPLORE]

---

## SUBGOAL_001_ALT [SUBGOAL]
**Parent Node**: STRATEGY_001

**Goal**: Establish the AM-GM inequality for n non-negative reals using a simpler approach.

**Strategy**: Use the fact that for non-negative reals, we can apply AM-GM directly by cases. If n = 0, trivial. If n > 0, use Real.rpow_arith_mean_le_arith_mean_rpow or similar direct application.

**Status**: [PROMISING]
