import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Algebra.Order.BigOperators.Ring.Finset

-- Proof that for all positive reals a,b,c,d: a²/b + b²/c + c²/d + d²/a ≥ a + b + c + d
theorem cyclic_sum_inequality (a b c d : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c) (hd : 0 < d) :
  a^2 / b + b^2 / c + c^2 / d + d^2 / a ≥ a + b + c + d := by
  -- Apply <PERSON><PERSON><PERSON><PERSON>'s lemma (<PERSON><PERSON><PERSON>'s lemma) using sq_sum_div_le_sum_sq_div
  -- We use the fact that (a+b+c+d)²/(a+b+c+d) = a+b+c+d
  have h_pos : 0 < a + b + c + d := by linarith [ha, hb, hc, hd]
  have h_simp : (a + b + c + d) ^ 2 / (a + b + c + d) = a + b + c + d := by
    field_simp [ne_of_gt h_pos]
    ring
  -- Apply the theorem directly
  let f : Fin 4 → ℝ := ![a, b, c, d]
  let g : Fin 4 → ℝ := ![b, c, d, a]
  have hg : ∀ i ∈ ({0, 1, 2, 3} : Finset (Fin 4)), 0 < g i := by
    intro i _
    fin_cases i <;> simp [g] <;> assumption
  have key := Finset.sq_sum_div_le_sum_sq_div {0, 1, 2, 3} f hg
  simp [f, g] at key
  -- Convert the parentheses grouping
  have h_eq1 : a + (b + (c + d)) = a + b + c + d := by ring
  have h_eq2 : b + (c + (d + a)) = a + b + c + d := by ring
  have h_eq3 : a ^ 2 / b + (b ^ 2 / c + (c ^ 2 / d + d ^ 2 / a)) =
               a ^ 2 / b + b ^ 2 / c + c ^ 2 / d + d ^ 2 / a := by ring
  rw [h_eq1, h_eq2, h_simp, h_eq3] at key
  exact key
