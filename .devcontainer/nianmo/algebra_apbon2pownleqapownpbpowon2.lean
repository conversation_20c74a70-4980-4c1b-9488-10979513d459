import Mathlib.Analysis.Convex.Jensen
import Mathlib.Analysis.Calculus.Deriv.Basic
import Mathlib.Data.Real.Basic
import Mathlib.Tactic

-- Proof using convexity
theorem power_mean_inequality_convex (a b : ℝ) (n : ℕ) (ha : 0 < a) (hb : 0 < b) :
  ((a + b) / 2) ^ n ≤ (a ^ n + b ^ n) / 2 := by
  -- Use the fact that x^n is convex on [0,∞) and apply <PERSON>'s inequality
  have convex_pow : ConvexOn ℝ (Set.Ici 0) (fun x : ℝ => x ^ n) := convexOn_pow n
  -- Apply the two-point Jensen inequality
  have ha_mem : a ∈ Set.Ici (0 : ℝ) := le_of_lt ha
  have hb_mem : b ∈ Set.Ici (0 : ℝ) := le_of_lt hb
  -- Use the convexity directly for the two-point case
  have := convex_pow.2 ha_mem hb_mem (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (1/2 : ℝ) + 1/2 = 1)
  convert this using 1
  · simp [smul_eq_mul]; ring
  · simp [smul_eq_mul]; ring



-- Main theorem: For all positive reals a, b and every natural number n, ((a + b)/2)ⁿ ≤ (aⁿ + bⁿ)/2
theorem power_mean_inequality (a b : ℝ) (n : ℕ) (ha : 0 < a) (hb : 0 < b) :
  ((a + b) / 2) ^ n ≤ (a ^ n + b ^ n) / 2 :=
  power_mean_inequality_convex a b n ha hb
