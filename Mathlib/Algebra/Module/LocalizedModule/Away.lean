/-
Copyright (c) 2025 Yongle Hu. All rights reserved.
Released under Apache 2.0 license as described in the file LICENSE.
Authors: <AUTHORS>
-/
import Mathlib.Algebra.Module.LocalizedModule.Basic

/-!
# Localizations of modules away from an element
-/

/-- Given `x : R` and `f : M →ₗ[R] M'`, IsLocalization.Away x f` states that `M'`
  is isomorphic to the localization of `M` at the submonoid generated by `x`. -/
protected abbrev IsLocalizedModule.Away {R M M' : Type*} [CommSemiring R] (x : R) [AddCommMonoid M]
    [Module R M] [AddCommMonoid M'] [Module R M'] (f : M →ₗ[R] M') :=
  IsLocalizedModule (Submonoid.powers x) f

/-- Given `x : R`, `LocalizedModule.Away x M` is the localization of `M` at the
  submonoid generated by `x`. -/
protected abbrev LocalizedModule.Away {R : Type*} [CommSemiring R] (x : R)
    (M : Type*) [AddCommMonoid M] [Module R M] :=
  LocalizedModule (Submonoid.powers x) M
